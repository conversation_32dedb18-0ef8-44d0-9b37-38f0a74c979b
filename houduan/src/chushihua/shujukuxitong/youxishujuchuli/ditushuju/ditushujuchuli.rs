#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::ditu_sql_kongzhi::ditu_sql_kongzhi;
use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use super::ditushujujiegouti::{ditu_ziduan_jieguo, ditu_quanbu_xinxi_jieguo, ditu_huancun_caozuo_jieguo};
use sqlx::Row;
use std::collections::HashMap;
use serde_json;

/// 地图数据处理类
pub struct ditushujuchuli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditushujuchuli {
    /// 创建新的地图数据处理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis连接的地图数据处理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie.clone());
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 根据地图ID和字段名获取地图数据
    /// 如果字段名是"quanbu_xinxi"，则获取全部信息并使用Redis缓存
    /// 否则只获取单个字段，不使用缓存
    pub async fn huoqu_ditu_shuju(&self, ditu_id: &str, ziduan_ming: &str) -> anyhow::Result<serde_json::Value> {
        // 验证字段名
        if !ditu_sql_kongzhi::jiancha_ziduan_hefa(ziduan_ming) {
            ditu_rizhi_kongzhi::ziduan_ming_yanzheng_shibai(ziduan_ming);
            return Err(anyhow::anyhow!("字段名不合法或不存在: {}", ziduan_ming));
        }

        // 检查是否是获取全部信息
        if ziduan_ming == ditu_sql_kongzhi::quanbu_xinxi_ziduan {
            // 获取全部信息，使用Redis缓存
            match self.huoqu_ditu_quanbu_xinxi(ditu_id).await {
                Ok(Some(jieguo)) => Ok(serde_json::to_value(jieguo)?),
                Ok(None) => {
                    ditu_rizhi_kongzhi::ditu_shuju_bu_cunzai(ditu_id);
                    Ok(serde_json::Value::Null)
                }
                Err(e) => {
                    ditu_rizhi_kongzhi::huoqu_ditu_quanbu_xinxi_shibai(ditu_id, &e.to_string());
                    Err(e)
                }
            }
        } else {
            // 获取单个字段，不使用缓存
            match self.huoqu_ditu_ziduan(ditu_id, ziduan_ming).await {
                Ok(jieguo) => Ok(serde_json::to_value(jieguo)?),
                Err(e) => {
                    ditu_rizhi_kongzhi::huoqu_ditu_ziduan_shibai(ditu_id, ziduan_ming, &e.to_string());
                    Err(e)
                }
            }
        }
    }

    /// 获取地图单个字段数据（不使用缓存）
    pub async fn huoqu_ditu_ziduan(&self, ditu_id: &str, ziduan_ming: &str) -> anyhow::Result<ditu_ziduan_jieguo> {
        // 验证字段名安全性
        if !ditu_sql_kongzhi::yanzheng_ziduan_ming_anquan(ziduan_ming) {
            return Err(anyhow::anyhow!("字段名包含不安全字符: {}", ziduan_ming));
        }

        // 构建SQL查询
        let sql = ditu_sql_kongzhi::gouzao_ziduan_chaxun_sql(ziduan_ming);

        // 执行查询
        match sqlx::query(&sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(Some(row)) => {
                let ziduan_zhi: Option<String> = row.try_get(ziduan_ming).unwrap_or(None);
                ditu_rizhi_kongzhi::huoqu_ditu_ziduan_chenggong(ditu_id, ziduan_ming);
                Ok(ditu_ziduan_jieguo::new(
                    ditu_id.to_string(),
                    ziduan_ming.to_string(),
                    ziduan_zhi,
                ))
            }
            Ok(None) => {
                ditu_rizhi_kongzhi::ditu_shuju_bu_cunzai(ditu_id);
                Ok(ditu_ziduan_jieguo::new(
                    ditu_id.to_string(),
                    ziduan_ming.to_string(),
                    None,
                ))
            }
            Err(e) => {
                ditu_rizhi_kongzhi::shujuku_chaxun_shibai(&sql, &e.to_string());
                Err(e.into())
            }
        }
    }