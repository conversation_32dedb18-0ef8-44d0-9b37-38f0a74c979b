#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::shujuku_rizhiguanli::{shujukuxitong_rizhi_cuowu, shujukuxitong_rizhi_xinxi};

/// 地图数据处理日志控制类
pub struct ditu_rizhi_kongzhi;

impl ditu_rizhi_kongzhi {
    /// 模块名称常量
    const mokuai_ming: &'static str = "地图数据库操作";

    // ==================== 成功日志方法 ====================

    /// 获取地图单个字段成功
    pub fn huoqu_ditu_ziduan_chenggong(ditu_id: &str, ziduan_ming: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("获取地图字段成功，地图ID: {}，字段: {}", ditu_id, ziduan_ming));
    }

    /// 获取地图全部信息成功
    pub fn huoqu_ditu_quanbu_xinxi_chenggong(ditu_id: &str, laiyuan: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("获取地图全部信息成功，地图ID: {}，数据来源: {}", ditu_id, laiyuan));
    }

    /// 缓存地图数据成功
    pub fn huancun_ditu_shuju_chenggong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("缓存地图数据成功，地图ID: {}", ditu_id));
    }

    /// 清除地图缓存成功
    pub fn qingchu_ditu_huancun_chenggong(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("清除地图缓存成功，地图ID: {}", ditu_id));
    }

    // ==================== 错误日志方法 ====================

    /// 获取地图单个字段失败
    pub fn huoqu_ditu_ziduan_shibai(ditu_id: &str, ziduan_ming: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取地图字段失败，地图ID: {}，字段: {}，错误: {}", ditu_id, ziduan_ming, cuowu));
    }

    /// 获取地图全部信息失败
    pub fn huoqu_ditu_quanbu_xinxi_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("获取地图全部信息失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 字段名验证失败
    pub fn ziduan_ming_yanzheng_shibai(ziduan_ming: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("字段名验证失败，字段名: {}，原因: 字段名不合法或不存在", ziduan_ming));
    }

    /// 缓存地图数据失败
    pub fn huancun_ditu_shuju_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("缓存地图数据失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 清除地图缓存失败
    pub fn qingchu_ditu_huancun_shibai(ditu_id: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("清除地图缓存失败，地图ID: {}，错误: {}", ditu_id, cuowu));
    }

    /// 地图数据不存在
    pub fn ditu_shuju_bu_cunzai(ditu_id: &str) {
        shujukuxitong_rizhi_xinxi(Self::mokuai_ming, &format!("地图数据不存在，地图ID: {}", ditu_id));
    }

    /// Redis连接失败
    pub fn redis_lianjie_shibai(caozuo: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("Redis操作失败，操作: {}，错误: {}", caozuo, cuowu));
    }

    /// 数据库查询失败
    pub fn shujuku_chaxun_shibai(sql: &str, cuowu: &str) {
        shujukuxitong_rizhi_cuowu(Self::mokuai_ming, &format!("数据库查询失败，SQL: {}，错误: {}", sql, cuowu));
    }
}