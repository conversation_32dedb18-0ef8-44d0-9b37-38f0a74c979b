#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use houduan::chushihua::houduan_chushihua::houduan_chushihua;
use houduan::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditushujuchuli::ditushu<PERSON>chuli;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("=== 地图数据处理系统测试 ===");
    
    // 初始化系统
    println!("正在初始化系统...");
    let (_, mysql_lianjie, redis_lianjie) = match houduan_chushihua::houduan_chushihua_xitong().await {
        Ok(result) => result,
        Err(e) => {
            println!("系统初始化失败: {}", e);
            return Err(e);
        }
    };
    
    println!("系统初始化成功！");
    
    // 创建地图数据处理实例
    let ditu_chuliqii = ditushujuchuli::new_with_redis(mysql_lianjie, redis_lianjie);
    
    // 测试地图ID
    let test_ditu_id = "prontera";
    
    println!("\n=== 测试1: 获取单个字段 ===");
    match ditu_chuliqii.huoqu_ditu_ziduan(test_ditu_id, "ditu_mingcheng").await {
        Ok(jieguo) => {
            println!("原始结果: {:?}", jieguo);
        }
        Err(e) => {
            println!("获取单个字段失败: {}", e);
        }
    }
    
    println!("\n=== 测试2: 获取全部信息（第一次，应该从数据库） ===");
    match ditu_chuliqii.huoqu_ditu_quanbu_xinxi(test_ditu_id).await {
        Ok(Some(jieguo)) => {
            println!("原始结果: {:?}", jieguo);
            println!("数据来源: {}", jieguo.shuju_laiyuan);
            println!("字段数量: {}", jieguo.ziduan_shuliang());
        }
        Ok(None) => {
            println!("地图数据不存在");
        }
        Err(e) => {
            println!("获取全部信息失败: {}", e);
        }
    }
    
    println!("\n=== 测试3: 再次获取全部信息（应该从缓存） ===");
    match ditu_chuliqii.huoqu_ditu_quanbu_xinxi(test_ditu_id).await {
        Ok(Some(jieguo)) => {
            println!("原始结果: {:?}", jieguo);
            println!("数据来源: {}", jieguo.shuju_laiyuan);
            println!("是否来自缓存: {}", jieguo.laizi_huancun());
        }
        Ok(None) => {
            println!("地图数据不存在");
        }
        Err(e) => {
            println!("获取全部信息失败: {}", e);
        }
    }
    
    println!("\n=== 测试4: 使用统一接口获取单个字段 ===");
    match ditu_chuliqii.huoqu_ditu_shuju(test_ditu_id, "xianshi_mingcheng").await {
        Ok(jieguo) => {
            println!("原始JSON结果: {}", jieguo);
        }
        Err(e) => {
            println!("统一接口获取单个字段失败: {}", e);
        }
    }
    
    println!("\n=== 测试5: 使用统一接口获取全部信息 ===");
    match ditu_chuliqii.huoqu_ditu_shuju(test_ditu_id, "quanbu_xinxi").await {
        Ok(jieguo) => {
            println!("原始JSON结果: {}", jieguo);
        }
        Err(e) => {
            println!("统一接口获取全部信息失败: {}", e);
        }
    }
    
    println!("\n=== 测试6: 缓存管理 ===");
    
    // 检查缓存是否存在
    match ditu_chuliqii.jiancha_ditu_huancun_cunzai(test_ditu_id).await {
        Ok(cunzai) => {
            println!("缓存是否存在: {}", cunzai);
        }
        Err(e) => {
            println!("检查缓存失败: {}", e);
        }
    }
    
    // 获取缓存统计
    match ditu_chuliqii.huoqu_ditu_huancun_tongji().await {
        Ok(tongji) => {
            println!("缓存统计: {}", tongji);
        }
        Err(e) => {
            println!("获取缓存统计失败: {}", e);
        }
    }
    
    // 清除缓存
    match ditu_chuliqii.qingchu_ditu_huancun(test_ditu_id).await {
        Ok(jieguo) => {
            println!("清除缓存结果: {:?}", jieguo);
        }
        Err(e) => {
            println!("清除缓存失败: {}", e);
        }
    }
    
    println!("\n=== 测试7: 测试不存在的地图 ===");
    let fake_ditu_id = "nonexistent_map_12345";
    match ditu_chuliqii.huoqu_ditu_ziduan(fake_ditu_id, "ditu_mingcheng").await {
        Ok(jieguo) => {
            println!("不存在地图的查询结果: {:?}", jieguo);
        }
        Err(e) => {
            println!("查询不存在地图失败: {}", e);
        }
    }
    
    println!("\n=== 测试8: 测试无效字段名 ===");
    match ditu_chuliqii.huoqu_ditu_ziduan(test_ditu_id, "invalid_field_name").await {
        Ok(jieguo) => {
            println!("无效字段名查询结果: {:?}", jieguo);
        }
        Err(e) => {
            println!("无效字段名查询失败: {}", e);
        }
    }
    
    println!("\n=== 测试完成 ===");
    Ok(())
}
